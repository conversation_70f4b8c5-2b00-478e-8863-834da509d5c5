import requests
import json
import time
from typing import Dict, List, Any
from models.learning_models import ErrorType
from models.content_models import TestCase
from config import get_config

class CodeExecutor:
    """代码执行器 - 使用外部API安全执行Python代码"""
    
    def __init__(self, language: str = 'zh'):
        # 使用配置管理系统
        self.config = get_config()
        self.language = language
        
        # Judge0 API配置
        self.base_url = self.config.JUDGE0_BASE_URL
        self.api_key = self.config.JUDGE0_API_KEY
        self.headers = {
            "X-RapidAPI-Key": self.api_key,
            "X-RapidAPI-Host": self.config.JUDGE0_HOST,
            "Content-Type": "application/json"
        }
        self.python_language_id = self.config.PYTHON_LANGUAGE_ID
        self.timeout_seconds = self.config.JUDGE0_TIMEOUT
        
        # 双语错误消息
        self.messages = {
            'zh': {
                'output_mismatch': '期望输出: {expected}, 实际输出: {actual}',
                'all_tests_passed': '所有测试用例通过！',
                'output_incorrect': '输出结果不正确',
                'execution_service_error': '执行服务错误: {error}',
                'judge0_failed': 'Judge0 API 调用失败: {error}，回退到模拟执行',
                'network_error': '网络请求失败: {error}',
                'submission_failed': '提交代码执行失败: HTTP {status}',
                'execution_timeout': '代码执行超时',
                'compilation_error': '编译错误',
                'syntax_error': '语法错误: {error}',
                'name_error': '名称错误: {error}',
                'type_error': '类型错误: {error}',
                'runtime_error': '运行时错误: {error}',
                'execution_failed': '执行失败: {error}',
                'unknown_error': '未知错误'
            },
            'en': {
                'output_mismatch': 'Expected output: {expected}, Actual output: {actual}',
                'all_tests_passed': 'All test cases passed!',
                'output_incorrect': 'Output is incorrect',
                'execution_service_error': 'Execution service error: {error}',
                'judge0_failed': 'Judge0 API call failed: {error}, falling back to simulation',
                'network_error': 'Network request failed: {error}',
                'submission_failed': 'Code submission failed: HTTP {status}',
                'execution_timeout': 'Code execution timeout',
                'compilation_error': 'Compilation error',
                'syntax_error': 'Syntax error: {error}',
                'name_error': 'Name error: {error}',
                'type_error': 'Type error: {error}',
                'runtime_error': 'Runtime error: {error}',
                'execution_failed': 'Execution failed: {error}',
                'unknown_error': 'Unknown error'
            }
        }
        
    def get_message(self, key: str, **kwargs) -> str:
        """获取本地化消息"""
        message_dict = self.messages.get(self.language, self.messages['zh'])
        message = message_dict.get(key, message_dict['unknown_error'])
        return message.format(**kwargs) if kwargs else message
        
    def health_check(self) -> bool:
        """检查Judge0 API是否可用"""
        try:
            response = requests.get(
                f"{self.base_url}/about",
                headers={
                    "X-RapidAPI-Key": self.api_key,
                    "X-RapidAPI-Host": self.config.JUDGE0_HOST
                },
                timeout=5
            )
            if response.status_code == 200:
                print("✅ Judge0 API健康检查通过")
                return True
            else:
                print(f"❌ Judge0 API健康检查失败 - 状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Judge0 API健康检查异常: {str(e)}")
            return False
    
    def execute_code(self, code: str, test_cases: List[TestCase]) -> Dict[str, Any]:
        """
        执行代码并与测试用例比较
        返回执行结果，包含正确性、错误类型和反馈信息
        """
        try:
            # 对每个测试用例执行代码
            all_passed = True
            error_info = None
            output_results = []
            
            for test_case in test_cases:
                result = self._execute_single_test(code, test_case)
                
                if not result['success']:
                    all_passed = False
                    error_info = result
                    break
                
                if not result['output_matches']:
                    all_passed = False
                    error_info = {
                        'error_type': ErrorType.LOGIC_ERROR.value,
                        'expected_output': test_case.expected_output,
                        'actual_output': result['output'],
                        'error_message': self.get_message('output_mismatch', 
                                                        expected=test_case.expected_output, 
                                                        actual=result['output'])
                    }
                    break
                
                output_results.append(result['output'])
            
            if all_passed:
                return {
                    'is_correct': True,
                    'error_type': ErrorType.NO_ERROR.value,
                    'output': '; '.join(output_results),
                    'message': self.get_message('all_tests_passed'),
                    'exec_time': 0.1
                }
            else:
                # 确保error_info不为None
                if error_info is None:
                    error_info = {
                        'error_type': ErrorType.RUNTIME_ERROR.value,
                        'error_message': self.get_message('unknown_error'),
                        'actual_output': '',
                        'expected_output': '',
                        'exec_time': 0.0
                    }
                
                return {
                    'is_correct': False,
                    'error_type': error_info['error_type'],
                    'error_message': error_info.get('error_message', ''),
                    'output': error_info.get('actual_output', ''),
                    'expected_output': error_info.get('expected_output', ''),
                    'exec_time': error_info.get('exec_time', 0.0)
                }
                
        except Exception as e:
            return {
                'is_correct': False,
                'error_type': ErrorType.RUNTIME_ERROR.value,
                'error_message': self.get_message('execution_service_error', error=str(e))
            }
    
    def _execute_single_test(self, code: str, test_case: TestCase) -> Dict[str, Any]:
        """执行单个测试用例"""
        try:
            # 构建完整的代码（包含输入数据）
            full_code = self._prepare_code_with_input(code, test_case.input_data)
            
            # 提交到Judge0 API
            submission_data = {
                "language_id": self.python_language_id,
                "source_code": full_code,
                "stdin": "",
                "expected_output": test_case.expected_output.strip()
            }
            
            # 首先尝试使用Judge0 API，失败时使用模拟执行作为备选
            try:
                result = self._execute_with_judge0(submission_data, test_case)
                print("🎯 使用Judge0 API执行代码")
                return result
            except Exception as judge0_error:
                print(self.get_message('judge0_failed', error=judge0_error))
                print("⚠️ 回退到本地模拟执行")
                return self._simulate_execution(code, test_case)
            
        except requests.RequestException as e:
            return {
                'success': False,
                'error_type': ErrorType.RUNTIME_ERROR.value,
                'error_message': self.get_message('network_error', error=str(e))
            }
        except Exception as e:
            return {
                'success': False,
                'error_type': ErrorType.RUNTIME_ERROR.value,
                'error_message': str(e)
            }
    
    def _execute_with_judge0(self, submission_data: Dict[str, Any], test_case: TestCase) -> Dict[str, Any]:
        """使用Judge0 API执行代码"""
        # 添加Judge0推荐的参数
        submission_data.update({
            "wait": False,  # 异步处理
            "base64_encoded": False  # 明确指定编码方式
        })

        # 提交代码执行请求
        response = requests.post(
            f"{self.base_url}/submissions",
            headers=self.headers,
            json=submission_data,
            timeout=15
        )

        if response.status_code != 201:
            raise Exception(self.get_message('submission_failed', status=response.status_code))

        submission = response.json()
        token = submission["token"]
        print(f"✅ Judge0 API调用成功 - Token: {token}")

        # 等待执行完成并获取结果
        result = self._wait_for_result(token)

        # 解析结果并添加成功日志
        parsed_result = self._parse_execution_result(result, test_case)

        # 记录执行结果状态
        status_id = result.get("status", {}).get("id", 0)
        status_description = result.get("status", {}).get("description", "Unknown")
        print(f"✅ Judge0执行完成 - 状态: {status_id} ({status_description})")

        return parsed_result
    
    def _simulate_execution(self, code: str, test_case: TestCase) -> Dict[str, Any]:
        """模拟代码执行（用于开发和测试）"""
        try:
            # 简单的本地执行（仅用于演示，生产环境必须使用沙箱）
            import io
            import sys
            from contextlib import redirect_stdout
            import time
            
            start_time = time.time()
            
            # 准备执行环境
            old_stdout = sys.stdout
            output_buffer = io.StringIO()
            
            # 构建代码环境
            exec_globals = {}
            if test_case.input_data:
                # 模拟input()函数
                input_lines = test_case.input_data.strip().split('\n')
                input_iter = iter(input_lines)
                exec_globals['input'] = lambda: next(input_iter, '')
            
            try:
                with redirect_stdout(output_buffer):
                    exec(code, exec_globals)
                
                exec_time = time.time() - start_time
                output = output_buffer.getvalue().strip()
                expected = test_case.expected_output.strip()
                
                return {
                    'success': True,
                    'output': output,
                    'output_matches': output == expected,
                    'exec_time': exec_time
                }
                
            except SyntaxError as e:
                return {
                    'success': False,
                    'error_type': ErrorType.SYNTAX_ERROR.value,
                    'error_message': self.get_message('syntax_error', error=str(e)),
                    'exec_time': 0.0
                }
            except NameError as e:
                return {
                    'success': False,
                    'error_type': ErrorType.NAME_ERROR.value,
                    'error_message': self.get_message('name_error', error=str(e)),
                    'exec_time': 0.0
                }
            except TypeError as e:
                return {
                    'success': False,
                    'error_type': ErrorType.TYPE_ERROR.value,
                    'error_message': self.get_message('type_error', error=str(e)),
                    'exec_time': 0.0
                }
            except Exception as e:
                return {
                    'success': False,
                    'error_type': ErrorType.RUNTIME_ERROR.value,
                    'error_message': self.get_message('runtime_error', error=str(e)),
                    'exec_time': 0.0
                }
            finally:
                sys.stdout = old_stdout
                
        except Exception as e:
            return {
                'success': False,
                'error_type': ErrorType.RUNTIME_ERROR.value,
                'error_message': self.get_message('execution_failed', error=str(e))
            }
    
    def _prepare_code_with_input(self, code: str, input_data: str) -> str:
        """准备包含输入数据的代码"""
        if input_data:
            # 为有输入的代码准备mock input函数
            input_lines = input_data.strip().split('\n')
            mock_input = f"""
input_data = {repr(input_lines)}
input_index = 0
def input():
    global input_index
    if input_index < len(input_data):
        result = input_data[input_index]
        input_index += 1
        return result
    return ""

{code}
"""
            return mock_input
        return code
    
    def _wait_for_result(self, token: str, max_wait: int = 10) -> Dict[str, Any]:
        """等待代码执行完成"""
        # Judge0推荐的轮询间隔
        poll_intervals = [0.1, 0.2, 0.5, 1.0]  # 逐渐增加间隔
        
        for attempt in range(max_wait):
            response = requests.get(
                f"{self.base_url}/submissions/{token}",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                # Judge0状态：1=队列中, 2=处理中, 3=已完成
                if result["status"]["id"] not in [1, 2]:
                    return result
            
            # 使用智能轮询间隔
            interval = poll_intervals[min(attempt, len(poll_intervals) - 1)]
            time.sleep(interval)
        
        raise Exception(self.get_message('execution_timeout'))
    
    def _parse_execution_result(self, result: Dict[str, Any], test_case: TestCase) -> Dict[str, Any]:
        """解析执行结果"""
        status_id = result["status"]["id"]
        
        # 安全获取time字段，确保是float类型
        def safe_get_time(result_dict: Dict[str, Any], key: str, default: float = 0.0) -> float:
            value = result_dict.get(key, default)
            if isinstance(value, str):
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return default
            return float(value) if value is not None else default
        
        if status_id == 3:  # Accepted
            output = (result.get("stdout") or "").strip()
            expected = test_case.expected_output.strip()
            
            return {
                'success': True,
                'output': output,
                'output_matches': output == expected,
                'exec_time': safe_get_time(result, "time"),
                # 新增教育性指标
                'memory_usage': result.get("memory", 0),  # 内存使用(KB)
                'wall_time': safe_get_time(result, "wall_time"),  # 总时间
                'exit_code': result.get("exit_code", 0),    # 退出码
                'efficiency_score': self._calculate_efficiency_score(result)
            }
        elif status_id == 4:  # Wrong Answer
            return {
                'success': False,
                'error_type': ErrorType.LOGIC_ERROR.value,
                'actual_output': (result.get("stdout") or "").strip(),
                'expected_output': test_case.expected_output.strip(),
                'error_message': self.get_message('output_incorrect')
            }
        elif status_id == 6:  # Compilation Error
            return {
                'success': False,
                'error_type': ErrorType.SYNTAX_ERROR.value,
                'error_message': result.get("compile_stderr", self.get_message('compilation_error'))
            }
        elif status_id == 5:  # Time Limit Exceeded
            return {
                'success': False,
                'error_type': ErrorType.TIMEOUT_ERROR.value,
                'error_message': self.get_message('execution_timeout')
            }
        else:  # Runtime Error or other
            stderr = result.get("stderr", "")
            if "SyntaxError" in stderr:
                error_type = ErrorType.SYNTAX_ERROR.value
            elif "NameError" in stderr:
                error_type = ErrorType.NAME_ERROR.value
            elif "TypeError" in stderr:
                error_type = ErrorType.TYPE_ERROR.value
            elif "File too large" in stderr or "infinite loop" in stderr.lower():
                error_type = ErrorType.TIMEOUT_ERROR.value
            else:
                error_type = ErrorType.RUNTIME_ERROR.value
            
            return {
                'success': False,
                'error_type': error_type,
                'error_message': stderr or self.get_message('runtime_error', error='Unknown'),
                # 即使出错也记录性能数据
                'exec_time': safe_get_time(result, "time"),
                'memory_usage': result.get("memory", 0),
                'exit_code': result.get("exit_code", -1)
            }
    
    def _calculate_efficiency_score(self, result: Dict[str, Any]) -> float:
        """计算代码效率分数(0-100)"""
        time_score = max(0, 100 - (float(result.get("time", 0)) * 20))  # 时间越短分数越高
        memory_score = max(0, 100 - (int(result.get("memory", 0)) / 1000))  # 内存越少分数越高
        return round((time_score + memory_score) / 2, 2)