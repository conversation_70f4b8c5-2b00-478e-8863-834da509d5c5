<template>
  <div class="admin-analytics">
    <div class="page-header">
      <h1>📊 {{ $t('admin.analytics.title') }}</h1>
      <button @click="loadAnalytics" class="btn btn-primary">
        🔄 {{ $t('common.refresh') }}
      </button>
    </div>

    <!-- 概览统计 -->
    <div class="overview-section">
      <h2>📈 {{ $t('admin.analytics.platformOverview') }}</h2>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-content">
            <h3>{{ analytics?.overview?.total_users || 0 }}</h3>
            <p>{{ $t('admin.dashboard.stats.totalUsers') }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📚</div>
          <div class="stat-content">
            <h3>{{ analytics?.overview?.total_lessons || 0 }}</h3>
            <p>{{ $t('admin.dashboard.stats.totalLessons') }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💻</div>
          <div class="stat-content">
            <h3>{{ analytics?.overview?.total_exercises || 0 }}</h3>
            <p>{{ $t('admin.dashboard.stats.totalExercises') }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📝</div>
          <div class="stat-content">
            <h3>{{ analytics?.overview?.total_submissions || 0 }}</h3>
            <p>{{ $t('admin.dashboard.stats.totalSubmissions') }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <h3>{{ analytics?.overview?.lesson_completion_rate || 0 }}%</h3>
            <p>{{ $t('admin.analytics.lessonCompletionRate') }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🎯</div>
          <div class="stat-content">
            <h3>{{ analytics?.overview?.exercise_success_rate || 0 }}%</h3>
            <p>{{ $t('admin.analytics.exerciseSuccessRate') }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误类型分析 -->
    <div class="analysis-section">
      <h2>🐛 {{ $t('admin.analytics.errorTypeAnalysis') }}</h2>
      <div class="error-analysis-card">
        <div class="chart-container">
          <div v-if="analytics?.error_analysis?.length" class="error-stats-grid">
            <div 
              v-for="error in analytics.error_analysis" 
              :key="error.error_type"
              class="error-stat-card"
            >
              <div class="error-icon">⚠️</div>
              <div class="error-details">
                <div class="error-type">{{ getErrorTypeName(error.error_type) }}</div>
                <div class="error-count">{{ error.count }} {{ $t('dashboard.errorPatterns.times') }}</div>
              </div>
            </div>
          </div>
          <div v-else class="no-data">
            {{ $t('admin.analytics.noErrorData') }}
          </div>
        </div>
      </div>
    </div>

    <!-- 平台学习分析 -->
    <div class="analysis-section" v-if="platformAnalytics">
      <h2>📊 {{ $t('admin.analytics.platformLearningAnalysis') }}</h2>
      <div class="quality-analysis-card">
        <div class="quality-stats-grid">
          <div class="quality-stat">
            <div class="stat-icon">👥</div>
            <div class="stat-content">
              <h3>{{ platformAnalytics.total_learners || 0 }}</h3>
              <p>{{ $t('admin.analytics.totalLearners') }}</p>
            </div>
          </div>
          
          <div class="quality-stat">
            <div class="stat-icon">🎯</div>
            <div class="stat-content">
              <h3>{{ platformAnalytics.avg_success_rate || 0 }}%</h3>
              <p>{{ $t('admin.analytics.avgSuccessRate') }}</p>
            </div>
          </div>
          
          <div class="quality-stat">
            <div class="stat-icon">✨</div>
            <div class="stat-content">
              <h3>{{ platformAnalytics.avg_code_quality || 0 }}</h3>
              <p>{{ $t('admin.analytics.avgCodeQuality') }}</p>
            </div>
          </div>
          
          <div class="quality-stat">
            <div class="stat-icon">🔥</div>
            <div class="stat-content">
              <h3>{{ platformAnalytics.active_learners || 0 }}</h3>
              <p>{{ $t('admin.analytics.activeLearners') }}</p>
            </div>
          </div>
        </div>
        
        <!-- 常见错误分析 -->
        <div v-if="platformAnalytics.common_errors" class="common-errors-section">
          <h3>🐛 {{ $t('admin.analytics.commonErrors') }}</h3>
          <div class="error-stats-grid">
            <div 
              v-for="(count, errorType) in platformAnalytics.common_errors" 
              :key="errorType"
              class="error-stat-card"
            >
              <div class="error-icon">⚠️</div>
              <div class="error-details">
                <div class="error-type">{{ getErrorTypeName(errorType) }}</div>
                <div class="error-count">{{ count }} {{ $t('dashboard.errorPatterns.times') }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 练习难度分析 -->
        <div v-if="sortedExerciseDifficulty.length" class="exercise-difficulty-section">
          <h3>📈 {{ $t('admin.analytics.exerciseDifficulty') }}</h3>
          <div class="difficulty-list">
            <div 
              v-for="exercise in sortedExerciseDifficulty" 
              :key="exercise.id"
              class="difficulty-item"
            >
              <div class="exercise-info">
                <span class="exercise-title">{{ exercise.title }}</span>
                <span class="exercise-attempts">{{ exercise.total_attempts }} {{ $t('admin.analytics.attempts') }}</span>
              </div>
              <div class="difficulty-indicator">
                <div class="difficulty-bar">
                  <div class="difficulty-fill" 
                       :style="{ width: `${exercise.success_rate}%` }"
                       :class="getDifficultyClass(exercise.success_rate)"></div>
                </div>
                <span class="success-rate">{{ exercise.success_rate }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 活跃用户排行 -->
    <div class="analysis-section">
      <h2>🏆 {{ $t('admin.analytics.mostActiveUsers') }}</h2>
      <div class="active-users-card">
        <div v-if="analytics?.active_users?.length" class="users-ranking">
          <div 
            v-for="(user, index) in analytics.active_users" 
            :key="user.user_id"
            class="ranking-item"
          >
            <div class="ranking-number">
              <span class="rank" :class="getRankClass(index)">{{ index + 1 }}</span>
            </div>
            <div class="user-info">
              <span class="username">{{ user.username }}</span>
              <span class="submission-count">{{ user.submission_count }} {{ $t('admin.analytics.submissions') }}</span>
            </div>
            <div class="ranking-badge" :class="getRankClass(index)">
              {{ getRankEmoji(index) }}
            </div>
          </div>
        </div>
        <div v-else class="no-data">
          {{ $t('admin.analytics.noActiveUserData') }}
        </div>
      </div>
    </div>

    <!-- 最近7天活动趋势 -->
    <div class="analysis-section">
      <h2>📅 {{ $t('admin.analytics.recentActivityTrend') }}</h2>
      <div class="activity-trend-card">
        <div v-if="analytics?.activity_trend?.length" class="trend-chart">
          <div class="trend-header">
            <span>{{ $t('admin.analytics.last7DaysSubmissions') }}</span>
          </div>
          <div class="trend-bars">
            <div 
              v-for="day in analytics.activity_trend" 
              :key="day.date"
              class="trend-bar-item"
              :title="`${formatTrendDate(day.date)}: ${day.submissions} 次提交`"
            >
              <div class="trend-bar">
                <div 
                  class="trend-bar-fill" 
                  :style="{ height: calculateBarHeight(day.submissions) }"
                >
                  <span class="bar-value">{{ day.submissions }}</span>
                </div>
              </div>
              <div class="trend-date">{{ formatTrendDate(day.date) }}</div>
              <div class="trend-count">{{ day.submissions }}</div>
            </div>
          </div>
        </div>
        <div v-else class="no-data">
          {{ $t('admin.analytics.noActivityTrendData') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { adminAPI } from '../adminApi.js'

export default {
  name: 'AdminAnalytics',
  setup() {
    const { t } = useI18n()
    const analytics = ref(null)
    const platformAnalytics = ref(null)
    const loading = ref(false)

    const maxErrorCount = computed(() => {
      if (!analytics.value?.error_analysis?.length) return 1
      return Math.max(...analytics.value.error_analysis.map(e => e.count))
    })

    const maxCommonError = computed(() => {
      if (!platformAnalytics.value?.common_errors) return 1
      return Math.max(...Object.values(platformAnalytics.value.common_errors))
    })

    const maxDaySubmissions = computed(() => {
      if (!analytics.value?.activity_trend?.length) return 1
      return Math.max(...analytics.value.activity_trend.map(d => d.submissions))
    })

    const sortedExerciseDifficulty = computed(() => {
      if (!platformAnalytics.value?.exercise_difficulty) return []
      return Object.entries(platformAnalytics.value.exercise_difficulty)
        .map(([id, data]) => ({ id, ...data }))
        .sort((a, b) => a.success_rate - b.success_rate)
    })

    // 计算柱子高度，确保小数值也有合理的显示
    const calculateBarHeight = (submissions) => {
      if (!maxDaySubmissions.value || submissions === 0) return '8px'
      
      const percentage = (submissions / maxDaySubmissions.value) * 100
      
      // 使用对数缩放来更好地显示小数值
      if (percentage < 10) {
        // 对于小于10%的值，给予15-30%的显示高度
        const minHeight = 15 + (percentage / 10) * 15
        return `${minHeight}%`
      }
      
      return `${Math.min(100, percentage)}%`
    }

    const loadAnalytics = async () => {
      try {
        loading.value = true
        window.setLoading?.(true)
        
        // 加载原有分析数据
        const data = await adminAPI.getAnalytics()
        analytics.value = data
        
        // 调试信息
        console.log('Analytics data loaded:', data)
        if (data?.activity_trend) {
          console.log('Activity trend data:', data.activity_trend)
          console.log('Max day submissions:', Math.max(...data.activity_trend.map(d => d.submissions)))
        }
        
        // 加载新的平台分析数据
        try {
          const platformData = await adminAPI.getPlatformAnalytics()
          platformAnalytics.value = platformData
        } catch (platformError) {
          console.warn('加载平台分析数据失败:', platformError)
        }
        
      } catch (error) {
        console.error('加载分析数据失败:', error)
        window.showNotification?.(t('admin.analytics.loadAnalyticsError'), 'error')
      } finally {
        loading.value = false
        window.setLoading?.(false)
      }
    }

    const getErrorTypeName = (errorType) => {
      const errorNames = {
        'syntax_error': t('dashboard.errorPatterns.syntaxError'),
        'name_error': t('dashboard.errorPatterns.nameError'), 
        'type_error': t('dashboard.errorPatterns.typeError'),
        'runtime_error': t('dashboard.errorPatterns.runtimeError'),
        'logic_error': t('dashboard.errorPatterns.logicError'),
        'timeout_error': t('dashboard.errorPatterns.timeoutError'),
        'no_error': t('admin.analytics.noError')
      }
      return errorNames[errorType] || errorType
    }

    const getRankClass = (index) => {
      if (index === 0) return 'gold'
      if (index === 1) return 'silver'
      if (index === 2) return 'bronze'
      return 'normal'
    }

    const getDifficultyClass = (successRate) => {
      // 确保successRate是数字类型
      const rate = parseFloat(successRate) || 0

      if (rate >= 80) return 'easy'
      if (rate >= 60) return 'medium'
      if (rate >= 40) return 'hard'
      return 'very-hard'
    }

    const getRankEmoji = (index) => {
      if (index === 0) return '🥇'
      if (index === 1) return '🥈'
      if (index === 2) return '🥉'
      return '🏅'
    }

    const formatTrendDate = (dateString) => {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}/${date.getDate()}`
    }

    onMounted(() => {
      loadAnalytics()
    })

    return {
      analytics,
      platformAnalytics,
      loading,
      maxErrorCount,
      maxCommonError,
      maxDaySubmissions,
      sortedExerciseDifficulty,
      loadAnalytics,
      getErrorTypeName,
      getRankClass,
      getRankEmoji,
      getDifficultyClass,
      formatTrendDate,
      calculateBarHeight,
      t
    }
  }
}
</script>

<style scoped>
.admin-analytics {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-header h1 {
  color: var(--color-slate-800);
  margin: 0;
}

.overview-section,
.analysis-section {
  margin-bottom: 30px;
}

.overview-section h2,
.analysis-section h2 {
  color: var(--color-slate-800);
  margin-bottom: 20px;
  padding-left: 5px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 3rem;
  margin-right: 20px;
}

.stat-content h3 {
  font-size: 2rem;
  margin: 0;
  color: var(--color-slate-800);
}

.stat-content p {
  margin: 5px 0 0;
  color: var(--color-slate-600);
}

.error-analysis-card,
.active-users-card,
.activity-trend-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.error-chart {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.users-ranking {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--color-slate-50);
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.ranking-item:hover {
  transform: translateX(5px);
}

.ranking-number {
  min-width: 40px;
}

.rank {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-weight: bold;
  font-size: 14px;
}

.rank.gold {
  background: #ffd700;
  color: #996f00;
}

.rank.silver {
  background: #c0c0c0;
  color: #666;
}

.rank.bronze {
  background: #cd7f32;
  color: #8b4513;
}

.rank.normal {
  background: var(--color-slate-200);
  color: var(--color-slate-500);
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: 500;
  color: var(--color-slate-800);
}

.submission-count {
  font-size: 14px;
  color: var(--color-slate-600);
}

.ranking-badge {
  font-size: 24px;
}

.trend-chart {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.trend-header {
  font-weight: 500;
  color: var(--color-slate-800);
  text-align: center;
}

.trend-bars {
  display: flex;
  align-items: end;
  gap: 16px;
  height: 200px;
  padding: 20px 0;
  position: relative;
}

.trend-bars::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: #ddd;
}

.trend-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  transition: transform 0.2s ease;
  cursor: pointer;
}

.trend-bar-item:hover {
  transform: translateY(-2px);
}

.trend-bar-item:hover .trend-bar-fill {
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.trend-bar {
  width: 100%;
  height: 120px;
  background: var(--color-slate-50);
  border-radius: 4px 4px 0 0;
  position: relative;
  display: flex;
  align-items: end;
  border: 1px solid #e0e0e0;
}

.trend-bar-fill {
  width: 100%;
  background: linear-gradient(to top, var(--color-blue-600), var(--color-green-600));
  border-radius: 4px 4px 0 0;
  transition: height 0.5s ease;
  min-height: 8px; /* 增加最小高度，确保小数值也能看见 */
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bar-value {
  color: white;
  font-size: 11px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.trend-bar-item:hover .bar-value {
  opacity: 1;
}

.trend-date {
  font-size: 12px;
  color: var(--color-slate-600);
  font-weight: 500;
}

.trend-count {
  font-size: 14px;
  color: var(--color-slate-800);
  font-weight: bold;
}

.no-data {
  text-align: center;
  color: var(--color-slate-600);
  padding: 40px;
  font-size: 16px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-primary {
  background: var(--color-blue-600);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-blue-700);
}

/* 代码质量分析样式 */
.quality-analysis-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quality-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.quality-stat {
  background: var(--color-slate-50);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
}

.quality-stat:hover {
  transform: translateY(-2px);
}

.common-errors-section,
.exercise-difficulty-section {
  margin-top: 30px;
  padding-top: 24px;
  border-top: 1px solid var(--color-slate-200);
}

.common-errors-section h3,
.exercise-difficulty-section h3 {
  color: var(--color-slate-800);
  margin-bottom: 16px;
}

/* 错误统计网格 */
.error-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.error-stat-card {
  background: var(--color-white);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid var(--color-slate-200);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.error-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-color: var(--color-red-200);
}

.error-icon {
  font-size: 24px;
  opacity: 0.8;
}

.error-details {
  flex: 1;
}

.error-type {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-slate-700);
  margin-bottom: 4px;
}

.error-count {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-red-600);
}

.difficulty-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.difficulty-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--color-slate-50);
  border-radius: 8px;
}

.exercise-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.exercise-title {
  font-weight: 500;
  color: var(--color-slate-800);
  margin-bottom: 4px;
}

.exercise-attempts {
  font-size: 12px;
  color: var(--color-slate-600);
}

.difficulty-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
}

.difficulty-bar {
  width: 100px;
  height: 8px;
  background: var(--color-slate-100);
  border-radius: 4px;
  overflow: hidden;
}

.difficulty-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.difficulty-fill.easy {
  background: var(--color-green-700);
}

.difficulty-fill.medium {
  background: var(--color-amber-600);
}

.difficulty-fill.hard {
  background: var(--color-amber-700);
}

.difficulty-fill.very-hard {
  background: var(--color-red-600);
}

.success-rate {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-slate-800);
  min-width: 40px;
  text-align: right;
}
</style>